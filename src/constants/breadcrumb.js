import { <PERSON>IN<PERSON> } from "@link";

const BREADCRUMB = [
  // Admin pages
  {
    path: LINK.ADMIN.SETTING,
    items: [{ lang: "SETTINGS", url: LINK.ADMIN.SETTING }],
  },
  {
    path: LINK.ADMIN.ROLE_PLAY_COURSE_MANAGEMENT,
    items: [{ lang: "COURSE_MANAGEMENT", url: LINK.ADMIN.ROLE_PLAY_COURSE_MANAGEMENT }],
  },
  {
    path: LINK.ADMIN.ROLE_PLAY_COURSE_DETAIL.replace('{0}', ":id"),
    items: [
      { lang: "COURSE_MANAGEMENT", url: LINK.ADMIN.ROLE_PLAY_COURSE_MANAGEMENT },
      { lang: "COURSE_DETAIL" }
    ],
  },
  {
    path: LINK.ADMIN.ROLE_PLAY_AI_PERSONA_MANAGEMENT,
    items: [{ lang: "AI_PERSONA_MANAGEMENT", url: LINK.ADMIN.ROLE_PLAY_AI_PERSONA_MANAGEMENT }],
  },
  {
    path: LINK.ADMIN.ROLE_PLAY_AI_PERSONA_DETAIL.replace('{0}', ":id"),
    items: [{ lang: "AI_PERSONA_MANAGEMENT", url: LINK.ADMIN.ROLE_PLAY_AI_PERSONA_MANAGEMENT }],
  },
  {
    path: LINK.ADMIN.USER_MANAGEMENT,
    items: [{ lang: "USER_MANAGEMENT", url: LINK.ADMIN.USER_MANAGEMENT }],
  },
  {
    path: LINK.ADMIN.ROLE_PLAY_INSTRUCTION_MANAGEMENT,
    items: [{ lang: "INSTRUCTION_MANAGEMENT", url: LINK.ADMIN.ROLE_PLAY_INSTRUCTION_MANAGEMENT }],
  },
  {
    path: LINK.ADMIN.ROLE_PLAY_INSTRUCTION_DETAIL.replace('{0}', ":id"),
    items: [{ lang: "INSTRUCTION_MANAGEMENT", url: LINK.ADMIN.ROLE_PLAY_INSTRUCTION_MANAGEMENT }],
  },
  {
    path: LINK.ADMIN.ROLE_PLAY_COURSE_STATISTICS.replace('{0}', ":courseId"),
    items: [
      { lang: "COURSE_MANAGEMENT", url: LINK.ADMIN.ROLE_PLAY_COURSE_MANAGEMENT },
      { lang: "COURSE_STATISTICS" }
    ],
  },
  {
    path: LINK.ADMIN.ROLE_PLAY_STATISTICS_OVERVIEW,
    items: [{ lang: "STATISTICS", url: LINK.ADMIN.ROLE_PLAY_STATISTICS_OVERVIEW }],
  },

  // Student/User pages
  {
    path: LINK.COURSES,
    items: [{ lang: "COURSES", url: LINK.COURSES }],
  },
  {
    path: LINK.ACCOUNT,
    items: [{ lang: "ACCOUNT_SETTINGS", url: LINK.ACCOUNT }],
  },
  {
    path: LINK.EXCHANGE,
    items: [{ lang: "EXCHANGE", url: LINK.EXCHANGE }],
  },
  {
    path: LINK.PRICING,
    items: [{ lang: "PRICING", url: LINK.PRICING }],
  },
  {
    path: LINK.PAYMENT,
    items: [{ lang: "PAYMENT", url: LINK.PAYMENT }],
  },

];

export default BREADCRUMB;
