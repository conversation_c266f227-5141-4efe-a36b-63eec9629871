import React, { useState } from 'react';
import { Form } from 'antd';
import CourseInformationCard from './CourseInformationCard';

// Demo component để test CourseInformationCard
const CourseInformationCardDemo = () => {
  const [form] = Form.useForm();
  const [thumbnailId, setThumbnailId] = useState(null);
  const [isLoadingThumbnail, setIsLoadingThumbnail] = useState(false);
  const [selectedSimulationType, setSelectedSimulationType] = useState('');

  const handleUploadThumbnail = (file) => {
    console.log('Upload thumbnail:', file);
    setIsLoadingThumbnail(true);
    
    // Simulate upload
    setTimeout(() => {
      setThumbnailId('mock-thumbnail-id');
      setIsLoadingThumbnail(false);
    }, 2000);
  };

  const handleClearThumbnail = () => {
    console.log('Clear thumbnail');
    setThumbnailId(null);
  };

  const handleSimulationTypeChange = (value) => {
    console.log('Simulation type changed:', value);
    setSelectedSimulationType(value);
  };

  const handleShowGoogleSheetsImport = () => {
    console.log('Show Google Sheets import modal');
  };

  const handleFormSubmit = (values) => {
    console.log('Form submitted:', values);
  };

  return (
    <div style={{ padding: '24px', background: '#f5f5f5', minHeight: '100vh' }}>
      <h1>Course Information Card Demo</h1>
      
      <Form
        form={form}
        layout="vertical"
        onFinish={handleFormSubmit}
        initialValues={{
          status: 'draft',
          name: 'Khóa học mẫu',
          description: 'Đây là mô tả mẫu cho khóa học...',
          simulationType: 'Sale'
        }}
      >
        <CourseInformationCard
          form={form}
          thumbnailId={thumbnailId}
          isLoadingThumbnail={isLoadingThumbnail}
          selectedSimulationType={selectedSimulationType}
          onUploadThumbnail={handleUploadThumbnail}
          onClearThumbnail={handleClearThumbnail}
          onSimulationTypeChange={handleSimulationTypeChange}
          onShowGoogleSheetsImport={handleShowGoogleSheetsImport}
        />
      </Form>
    </div>
  );
};

export default CourseInformationCardDemo;
