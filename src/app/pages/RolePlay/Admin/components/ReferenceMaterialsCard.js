import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  Card, 
  Empty, 
  Button, 
  Space, 
  Tag, 
  Tooltip, 
  Typography, 
  Row, 
  Col,
  Badge,
  Dropdown,
  Menu,
  Input,
  Select
} from 'antd';
import {
  PlusOutlined,
  AppstoreOutlined,
  UnorderedListOutlined,
  FileTextOutlined,
  CloudDownloadOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  CalendarOutlined,
  SearchOutlined,
  FilterOutlined,
  SortAscendingOutlined,
  FolderOpenOutlined,
  LinkOutlined,
  FilePdfOutlined,
  FileWordOutlined,
  FileExcelOutlined,
  FilePptOutlined,
  FileImageOutlined,
  FileOutlined,
} from '@ant-design/icons';

import { BUTTON } from '@constant';
import AntButton from '@component/AntButton';
import { API } from '@api';

import './ReferenceMaterialsCard.scss';

const { Text, Title } = Typography;
const { Search } = Input;
const { Option } = Select;

const ReferenceMaterialsCard = ({
  references = [],
  referenceViewMode = 'card',
  onViewModeChange,
  onAddReference,
  onEditReference,
  onDeleteReference,
  onViewContent,
  formatDate,
  getFileIcon,
}) => {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [sortBy, setSortBy] = useState('newest');

  // Filter and sort references
  const filteredReferences = references
    .filter(ref => {
      const matchesSearch = ref.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           ref.url?.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesType = filterType === 'all' || ref.type === filterType;
      return matchesSearch && matchesType;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return new Date(b.createdAt) - new Date(a.createdAt);
        case 'oldest':
          return new Date(a.createdAt) - new Date(b.createdAt);
        case 'name':
          return (a.name || '').localeCompare(b.name || '');
        default:
          return 0;
      }
    });

  const getFileTypeIcon = (name, type) => {
    if (type === 'url') return <LinkOutlined />;
    
    const extension = name?.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return <FilePdfOutlined style={{ color: '#ff4d4f' }} />;
      case 'doc':
      case 'docx':
        return <FileWordOutlined style={{ color: '#1890ff' }} />;
      case 'xls':
      case 'xlsx':
        return <FileExcelOutlined style={{ color: '#52c41a' }} />;
      case 'ppt':
      case 'pptx':
        return <FilePptOutlined style={{ color: '#fa8c16' }} />;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return <FileImageOutlined style={{ color: '#722ed1' }} />;
      default:
        return <FileOutlined />;
    }
  };

  const sortMenu = (
    <Menu
      selectedKeys={[sortBy]}
      onClick={({ key }) => setSortBy(key)}
    >
      <Menu.Item key="newest">
        {t('NEWEST_FIRST', 'Mới nhất trước')}
      </Menu.Item>
      <Menu.Item key="oldest">
        {t('OLDEST_FIRST', 'Cũ nhất trước')}
      </Menu.Item>
      <Menu.Item key="name">
        {t('SORT_BY_NAME', 'Sắp xếp theo tên')}
      </Menu.Item>
    </Menu>
  );

  const renderEmptyState = () => (
    <div className="reference-empty-state">
      <div className="empty-state-content">
        <div className="empty-state-icon">
          <FolderOpenOutlined />
        </div>
        <Title level={4} className="empty-state-title">
          {t('NO_REFERENCE_MATERIALS', 'Chưa có tài liệu tham khảo')}
        </Title>
        <Text type="secondary" className="empty-state-description">
          {t('NO_REFERENCE_DESCRIPTION', 'Thêm tài liệu tham khảo để hỗ trợ quá trình học tập và đánh giá')}
        </Text>
        <AntButton
          type={BUTTON.DEEP_NAVY}
          size="large"
          icon={<PlusOutlined />}
          onClick={onAddReference}
          className="empty-state-button"
        >
          {t('ADD_FIRST_REFERENCE', 'Thêm tài liệu đầu tiên')}
        </AntButton>
      </div>
    </div>
  );

  const renderReferenceCard = (reference) => (
    <div key={reference._id} className="reference-card-wrapper">
      <Card className="reference-card modern" hoverable>
        <div className="reference-card-content">
          <div className="reference-header">
            <div className="reference-icon-wrapper">
              <div className="reference-icon">
                {getFileTypeIcon(reference.name, reference.type)}
              </div>
            </div>
            <div className="reference-title-section">
              <div className="reference-title">
                {reference.type === 'url' ? (
                  <a
                    href={reference.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="reference-link"
                    title={reference.name || reference.url}
                  >
                    {reference.name || reference.url}
                  </a>
                ) : (
                  <a
                    href={API.STREAM_ID.format(reference.fileId)}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="reference-link"
                    title={reference.name}
                  >
                    {reference.name}
                  </a>
                )}
              </div>
              <div className="reference-tags">
                <Tag
                  color={reference.type === 'url' ? 'blue' : 'green'}
                  className="reference-type-tag"
                >
                  {reference.type === 'url' ? t('URL_LINK', 'Link') : t('FILE', 'File')}
                </Tag>
                <Tag
                  color={reference.isPublic ? 'success' : 'warning'}
                  icon={reference.isPublic ? <EyeOutlined /> : <EyeInvisibleOutlined />}
                  className="reference-visibility-tag"
                >
                  {reference.isPublic ? t('PUBLIC', 'Công khai') : t('PRIVATE', 'Riêng tư')}
                </Tag>
              </div>
            </div>
          </div>

          {reference.createdAt && (
            <div className="reference-meta">
              <div className="reference-date">
                <CalendarOutlined />
                <span>{formatDate(reference.createdAt)}</span>
              </div>
            </div>
          )}

          <div className="reference-actions">
            <Space size="small">
              {reference.content && (
                <Tooltip title={t('VIEW_CONTENT', 'Xem nội dung')}>
                  <Button
                    type="text"
                    size="small"
                    icon={<FileTextOutlined />}
                    onClick={() => onViewContent(reference)}
                    className="action-button view"
                  />
                </Tooltip>
              )}
              {reference.type === 'file' && (
                <Tooltip title={t('DOWNLOAD', 'Tải xuống')}>
                  <Button
                    type="text"
                    size="small"
                    icon={<CloudDownloadOutlined />}
                    onClick={() => window.open(API.STREAM_ID.format(reference.fileId), '_blank')}
                    className="action-button download"
                  />
                </Tooltip>
              )}
              <Tooltip title={t('EDIT', 'Chỉnh sửa')}>
                <Button
                  type="text"
                  size="small"
                  icon={<EditOutlined />}
                  onClick={() => onEditReference(reference)}
                  className="action-button edit"
                />
              </Tooltip>
              <Tooltip title={t('DELETE', 'Xóa')}>
                <Button
                  type="text"
                  size="small"
                  icon={<DeleteOutlined />}
                  onClick={() => onDeleteReference(reference._id)}
                  className="action-button delete"
                />
              </Tooltip>
            </Space>
          </div>
        </div>
      </Card>
    </div>
  );

  const renderReferenceList = (reference) => (
    <div key={reference._id} className="reference-list-item">
      <div className="reference-list-icon">
        {getFileTypeIcon(reference.name, reference.type)}
      </div>
      <div className="reference-list-content">
        <div className="reference-list-title">
          {reference.type === 'url' ? (
            <a
              href={reference.url}
              target="_blank"
              rel="noopener noreferrer"
              className="reference-list-link"
            >
              {reference.name || reference.url}
            </a>
          ) : (
            <a
              href={API.STREAM_ID.format(reference.fileId)}
              target="_blank"
              rel="noopener noreferrer"
              className="reference-list-link"
            >
              {reference.name}
            </a>
          )}
        </div>
        <div className="reference-list-meta">
          <Space size="small">
            <Tag
              color={reference.type === 'url' ? 'blue' : 'green'}
              size="small"
            >
              {reference.type === 'url' ? t('URL_LINK', 'Link') : t('FILE', 'File')}
            </Tag>
            <Tag
              color={reference.isPublic ? 'success' : 'warning'}
              icon={reference.isPublic ? <EyeOutlined /> : <EyeInvisibleOutlined />}
              size="small"
            >
              {reference.isPublic ? t('PUBLIC', 'Công khai') : t('PRIVATE', 'Riêng tư')}
            </Tag>
            {reference.createdAt && (
              <Text type="secondary" className="reference-list-date">
                {formatDate(reference.createdAt)}
              </Text>
            )}
          </Space>
        </div>
      </div>
      <div className="reference-list-actions">
        <Space size="small">
          {reference.content && (
            <Tooltip title={t('VIEW_CONTENT', 'Xem nội dung')}>
              <Button
                type="text"
                size="small"
                icon={<FileTextOutlined />}
                onClick={() => onViewContent(reference)}
              />
            </Tooltip>
          )}
          {reference.type === 'file' && (
            <Tooltip title={t('DOWNLOAD', 'Tải xuống')}>
              <Button
                type="text"
                size="small"
                icon={<CloudDownloadOutlined />}
                onClick={() => window.open(API.STREAM_ID.format(reference.fileId), '_blank')}
              />
            </Tooltip>
          )}
          <Tooltip title={t('EDIT', 'Chỉnh sửa')}>
            <Button
              type="text"
              size="small"
              icon={<EditOutlined />}
              onClick={() => onEditReference(reference)}
            />
          </Tooltip>
          <Tooltip title={t('DELETE', 'Xóa')}>
            <Button
              type="text"
              size="small"
              icon={<DeleteOutlined />}
              onClick={() => onDeleteReference(reference._id)}
            />
          </Tooltip>
        </Space>
      </div>
    </div>
  );

  return (
    <Card
      className="reference-materials-card"
      title={
        <div className="card-title-section">
          <div className="card-title-main">
            <FolderOpenOutlined className="card-title-icon" />
            <Title level={4} className="card-title">
              {t('REFERENCE_MATERIALS', 'Tài liệu tham khảo')}
            </Title>
            {references.length > 0 && (
              <Badge count={references.length} className="reference-count-badge" />
            )}
          </div>
          <Text type="secondary" className="card-subtitle">
            {t('REFERENCE_MATERIALS_DESC', 'Quản lý tài liệu và nguồn tham khảo cho khóa học')}
          </Text>
        </div>
      }
      extra={
        <AntButton
          type={BUTTON.DEEP_NAVY}
          size="small"
          icon={<PlusOutlined />}
          onClick={onAddReference}
          className="add-reference-button"
        >
          {t('ADD_REFERENCE_MATERIALS', 'Thêm tài liệu')}
        </AntButton>
      }
    >
      {references.length === 0 ? (
        renderEmptyState()
      ) : (
        <div className="reference-materials-content">
          {/* Controls */}
          <div className="reference-controls">
            <div className="reference-controls-left">
              <Search
                placeholder={t('SEARCH_REFERENCES', 'Tìm kiếm tài liệu...')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="reference-search"
                allowClear
              />
              <Select
                value={filterType}
                onChange={setFilterType}
                className="reference-filter"
                suffixIcon={<FilterOutlined />}
              >
                <Option value="all">{t('ALL_TYPES', 'Tất cả')}</Option>
                <Option value="file">{t('FILES', 'File')}</Option>
                <Option value="url">{t('LINKS', 'Link')}</Option>
              </Select>
            </div>
            
            <div className="reference-controls-right">
              <Dropdown overlay={sortMenu} trigger={['click']}>
                <Button
                  size="small"
                  icon={<SortAscendingOutlined />}
                  className="sort-button"
                >
                  {t('SORT', 'Sắp xếp')}
                </Button>
              </Dropdown>
              
              <Button.Group size="small" className="view-mode-buttons">
                <Button
                  type={referenceViewMode === 'card' ? 'primary' : 'default'}
                  icon={<AppstoreOutlined />}
                  onClick={() => onViewModeChange('card')}
                >
                  {t('CARD_VIEW', 'Thẻ')}
                </Button>
                <Button
                  type={referenceViewMode === 'list' ? 'primary' : 'default'}
                  icon={<UnorderedListOutlined />}
                  onClick={() => onViewModeChange('list')}
                >
                  {t('LIST_VIEW', 'Danh sách')}
                </Button>
              </Button.Group>
            </div>
          </div>

          {/* Results info */}
          <div className="reference-results-info">
            <Text type="secondary">
              {t('SHOWING_RESULTS', 'Hiển thị')}: <strong>{filteredReferences.length}</strong> / {references.length} {t('DOCUMENTS', 'tài liệu')}
            </Text>
          </div>

          {/* Content */}
          {filteredReferences.length === 0 ? (
            <div className="no-results">
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description={t('NO_MATCHING_REFERENCES', 'Không tìm thấy tài liệu phù hợp')}
              />
            </div>
          ) : (
            <div className={`reference-content ${referenceViewMode}-view`}>
              {referenceViewMode === 'card' ? (
                <Row gutter={[16, 16]}>
                  {filteredReferences.map(reference => (
                    <Col xs={24} sm={12} lg={8} xl={6} key={reference._id}>
                      {renderReferenceCard(reference)}
                    </Col>
                  ))}
                </Row>
              ) : (
                <div className="reference-list">
                  {filteredReferences.map(renderReferenceList)}
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </Card>
  );
};

export default ReferenceMaterialsCard;
