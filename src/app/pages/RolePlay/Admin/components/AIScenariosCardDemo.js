import React, { useRef } from 'react';
import AIScenariosCard from './AIScenariosCard';

// Demo component để test AIScenariosCard
const AIScenariosCardDemo = () => {
  const scenarioTabsRef = useRef();

  const handleScenariosChange = (scenarios) => {
    console.log('Scenarios changed:', scenarios);
  };

  const handleTaskAdd = (task) => {
    console.log('Task added:', task);
  };

  const handleTaskUpdate = (task) => {
    console.log('Task updated:', task);
  };

  const handleTaskDelete = (taskId) => {
    console.log('Task deleted:', taskId);
  };

  return (
    <div style={{ padding: '24px', background: '#f5f5f5', minHeight: '100vh' }}>
      <h1>AI Scenarios Card Demo</h1>
      
      <h2>With Simulation Type</h2>
      <AIScenariosCard
        courseId="demo-course-id"
        selectedSimulationType="Sale"
        scenarioTabsRef={scenarioTabsRef}
        onScenariosChange={handleScenariosChange}
        onTaskAdd={handleTaskAdd}
        onTaskUpdate={handleTaskUpdate}
        onTaskDelete={handleTaskDelete}
      />

      <h2>Without Simulation Type</h2>
      <AIScenariosCard
        courseId="demo-course-id"
        selectedSimulationType=""
        scenarioTabsRef={scenarioTabsRef}
        onScenariosChange={handleScenariosChange}
        onTaskAdd={handleTaskAdd}
        onTaskUpdate={handleTaskUpdate}
        onTaskDelete={handleTaskDelete}
      />

      <h2>HR Simulation Type</h2>
      <AIScenariosCard
        courseId="demo-course-id"
        selectedSimulationType="HR"
        scenarioTabsRef={scenarioTabsRef}
        onScenariosChange={handleScenariosChange}
        onTaskAdd={handleTaskAdd}
        onTaskUpdate={handleTaskUpdate}
        onTaskDelete={handleTaskDelete}
      />
    </div>
  );
};

export default AIScenariosCardDemo;
