.ai-scenarios-card {
  .ant-card-head {
    background: linear-gradient(135deg, #f0f5ff 0%, #f9f0ff 100%);
    border-bottom: 1px solid #e6f0ff;
    padding: 0 24px;

    .ant-card-head-title {
      padding: 20px 0 16px 0;
    }
  }

  .card-title-section {
    .card-title-main {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 8px;

      .card-title-icon {
        color: #722ed1;
        font-size: 18px;
      }

      .card-title {
        margin: 0;
        color: #262626;
        font-size: 18px;
        font-weight: 600;
      }

      .ai-badge {
        .ant-badge-count {
          background: linear-gradient(135deg, #722ed1 0%, #1890ff 100%);
          border: none;
          box-shadow: 0 2px 4px rgba(114, 46, 209, 0.3);
          border-radius: 12px;
          padding: 0 8px;
          height: 20px;
          line-height: 20px;
          min-width: 20px;

          .ai-badge-icon {
            font-size: 12px;
            color: #fff;
          }
        }
      }
    }

    .card-subtitle-section {
      .card-subtitle {
        font-size: 13px;
        line-height: 1.4;
        display: block;
        margin-bottom: 8px;
      }

      .simulation-type-info {
        .simulation-emoji {
          font-size: 14px;
        }

        .simulation-type-text {
          font-weight: 500;
          font-size: 13px;
        }

        .simulation-info-icon {
          color: #8c8c8c;
          font-size: 12px;
          cursor: help;

          &:hover {
            color: #1890ff;
          }
        }
      }
    }
  }

  .ai-scenarios-content {
    // Alert for missing simulation type
    .simulation-type-alert {
      border-radius: 8px;
      border: 1px solid #91d5ff;
      background: #f0f8ff;

      .ant-alert-icon {
        color: #1890ff;
      }

      .ant-alert-message {
        color: #262626;
        font-weight: 500;
      }

      .ant-alert-description {
        color: #595959;
        line-height: 1.6;
      }
    }

    // Simulation context section
    .simulation-context {
      background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);
      border: 1px solid #d9f7be;
      border-radius: 12px;
      padding: 20px;
      margin-bottom: 24px;

      .context-header {
        margin-bottom: 8px;

        .context-emoji {
          font-size: 18px;
        }

        .context-title {
          font-size: 15px;
          color: #262626;
        }
      }

      .context-description {
        font-size: 14px;
        line-height: 1.6;
        color: #595959;
      }
    }

    // Scenarios tabs container
    .scenarios-tabs-container {
      margin-bottom: 24px;

      // Enhanced styling for AIScenarioTabs
      .ant-tabs {
        .ant-tabs-nav {
          margin-bottom: 24px;

          .ant-tabs-nav-wrap {
            .ant-tabs-nav-list {
              .ant-tabs-tab {
                border-radius: 8px 8px 0 0;
                border: 1px solid #f0f0f0;
                background: #fafafa;
                margin-right: 4px;
                transition: all 0.2s ease;

                &:hover {
                  background: #f0f8ff;
                  border-color: #d6e4ff;
                }

                &.ant-tabs-tab-active {
                  background: #fff;
                  border-color: #1890ff;
                  border-bottom-color: #fff;

                  .ant-tabs-tab-btn {
                    color: #1890ff;
                    font-weight: 600;
                  }
                }

                .ant-tabs-tab-btn {
                  padding: 12px 16px;
                  font-size: 14px;
                  font-weight: 500;
                }

                .ant-tabs-tab-remove {
                  margin-left: 8px;
                  color: #8c8c8c;
                  transition: color 0.2s ease;

                  &:hover {
                    color: #ff4d4f;
                  }
                }
              }
            }

            .ant-tabs-nav-operations {
              .ant-tabs-nav-add {
                border-radius: 8px;
                border: 1px dashed #d9d9d9;
                background: #fafafa;
                color: #595959;
                transition: all 0.2s ease;

                &:hover {
                  border-color: #1890ff;
                  color: #1890ff;
                  background: #f0f8ff;
                }
              }
            }
          }

          .ant-tabs-ink-bar {
            background: #1890ff;
            height: 3px;
            border-radius: 2px;
          }
        }

        .ant-tabs-content-holder {
          .ant-tabs-content {
            .ant-tabs-tabpane {
              // Enhanced form styling within tabs
              .ant-card {
                border-radius: 12px;
                border: 1px solid #f0f0f0;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
                margin-bottom: 16px;

                .ant-card-head {
                  background: #fafafa;
                  border-bottom: 1px solid #f0f0f0;
                  border-radius: 12px 12px 0 0;

                  .ant-card-head-title {
                    font-weight: 600;
                    color: #262626;
                  }
                }

                .ant-card-body {
                  padding: 20px;
                }
              }

              .ant-form {
                .ant-form-item {
                  margin-bottom: 20px;

                  .ant-form-item-label > label {
                    font-weight: 500;
                    color: #262626;
                  }

                  .ant-input,
                  .ant-select-selector,
                  .ant-input-number {
                    border-radius: 6px;
                    border: 1px solid #d9d9d9;
                    transition: all 0.2s ease;

                    &:hover {
                      border-color: #40a9ff;
                    }

                    &:focus,
                    &.ant-select-focused .ant-select-selector {
                      border-color: #1890ff;
                      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
                    }
                  }

                  textarea.ant-input {
                    resize: vertical;
                    min-height: 80px;
                  }
                }
              }
            }
          }
        }
      }
    }

    // Help section
    .scenarios-help {
      .help-alert {
        border-radius: 8px;
        border: 1px solid #b7eb8f;
        background: #f6ffed;

        .ant-alert-message {
          color: #262626;
          font-weight: 500;
          margin-bottom: 8px;
        }

        .help-content {
          ul {
            margin: 0;
            padding-left: 16px;

            li {
              color: #595959;
              line-height: 1.6;
              margin-bottom: 4px;
              font-size: 13px;

              &:last-child {
                margin-bottom: 0;
              }
            }
          }
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .ai-scenarios-card {
    .ant-card-head {
      padding: 0 16px;

      .card-title-section {
        .card-title-main {
          .card-title {
            font-size: 16px;
          }
        }

        .card-subtitle-section {
          .card-subtitle {
            font-size: 12px;
          }

          .simulation-type-info {
            .simulation-type-text {
              font-size: 12px;
            }
          }
        }
      }
    }

    .ai-scenarios-content {
      .simulation-context {
        padding: 16px;

        .context-header {
          .context-title {
            font-size: 14px;
          }
        }

        .context-description {
          font-size: 13px;
        }
      }

      .scenarios-tabs-container {
        .ant-tabs {
          .ant-tabs-nav {
            .ant-tabs-nav-wrap {
              .ant-tabs-nav-list {
                .ant-tabs-tab {
                  .ant-tabs-tab-btn {
                    padding: 10px 12px;
                    font-size: 13px;
                  }
                }
              }
            }
          }

          .ant-tabs-content-holder {
            .ant-tabs-content {
              .ant-tabs-tabpane {
                .ant-card {
                  .ant-card-body {
                    padding: 16px;
                  }
                }

                .ant-form {
                  .ant-form-item {
                    margin-bottom: 16px;
                  }
                }
              }
            }
          }
        }
      }

      .scenarios-help {
        .help-alert {
          .help-content {
            ul {
              li {
                font-size: 12px;
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .ai-scenarios-card {
    .ai-scenarios-content {
      .scenarios-tabs-container {
        .ant-tabs {
          .ant-tabs-nav {
            .ant-tabs-nav-wrap {
              .ant-tabs-nav-list {
                .ant-tabs-tab {
                  .ant-tabs-tab-btn {
                    padding: 8px 10px;
                    font-size: 12px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
