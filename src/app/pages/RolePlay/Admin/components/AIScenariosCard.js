import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Typography, Space, Badge, Tooltip, Alert } from 'antd';
import {
  RobotOutlined,
  ExperimentOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';

import AIScenarioTabs from './AIScenarioTabs';

import './AIScenariosCard.scss';

const { Text, Title } = Typography;

const AIScenariosCard = ({
  courseId,
  selectedSimulationType,
  scenarioTabsRef,
  onScenariosChange,
  onTaskAdd,
  onTaskUpdate,
  onTaskDelete,
}) => {
  const { t } = useTranslation();

  const getSimulationTypeInfo = (type) => {
    switch (type) {
      case 'Sale':
        return {
          emoji: '💼',
          color: '#52c41a',
          description: t('SALE_SIMULATION_DESC', '<PERSON>ô phỏng tình huống bán hàng và tương tác khách hàng'),
        };
      case 'Service':
        return {
          emoji: '🛎️',
          color: '#1890ff',
          description: t('SERVICE_SIMULATION_DESC', 'Mô phỏng tình huống dịch vụ khách hàng và hỗ trợ'),
        };
      case 'HR':
        return {
          emoji: '👥',
          color: '#722ed1',
          description: t('HR_SIMULATION_DESC', 'Mô phỏng tình huống nhân sự và quản lý'),
        };
      case 'Education':
        return {
          emoji: '📚',
          color: '#fa8c16',
          description: t('EDUCATION_SIMULATION_DESC', 'Mô phỏng tình huống giáo dục và đào tạo'),
        };
      default:
        return {
          emoji: '⚙️',
          color: '#8c8c8c',
          description: t('OTHER_SIMULATION_DESC', 'Mô phỏng tình huống khác'),
        };
    }
  };

  const simulationInfo = selectedSimulationType ? getSimulationTypeInfo(selectedSimulationType) : null;

  return (
    <Card
      className="ai-scenarios-card"
      title={
        <div className="card-title-section">
          <div className="card-title-main">
            <RobotOutlined className="card-title-icon" />
            <Title level={4} className="card-title">
              {t('AI_SCENARIOS', 'Kịch bản AI')}
            </Title>
            <Badge
              count={
                <Tooltip title={t('AI_POWERED', 'Được hỗ trợ bởi AI')}>
                  <ExperimentOutlined className="ai-badge-icon" />
                </Tooltip>
              }
              className="ai-badge"
            />
          </div>
          <div className="card-subtitle-section">
            <Text type="secondary" className="card-subtitle">
              {t('AI_SCENARIOS_DESC', 'Tạo và quản lý các kịch bản AI tương tác cho khóa học')}
            </Text>
            {simulationInfo && (
              <div className="simulation-type-info">
                <Space size="small">
                  <span className="simulation-emoji">{simulationInfo.emoji}</span>
                  <Text
                    className="simulation-type-text"
                    style={{ color: simulationInfo.color }}
                  >
                    {t(selectedSimulationType.toUpperCase() + '_SIMULATION', selectedSimulationType)}
                  </Text>
                  <Tooltip title={simulationInfo.description}>
                    <InfoCircleOutlined className="simulation-info-icon" />
                  </Tooltip>
                </Space>
              </div>
            )}
          </div>
        </div>
      }
    >
      <div className="ai-scenarios-content">
        {!selectedSimulationType ? (
          <Alert
            message={t('SELECT_SIMULATION_TYPE_FIRST', 'Vui lòng chọn loại mô phỏng trước')}
            description={t('SIMULATION_TYPE_REQUIRED_DESC', 'Bạn cần chọn loại mô phỏng trong phần thông tin khóa học để có thể tạo kịch bản AI phù hợp.')}
            type="info"
            icon={<InfoCircleOutlined />}
            showIcon
            className="simulation-type-alert"
          />
        ) : (
          <>
            {/* Simulation type context */}
            <div className="simulation-context">
              <div className="context-header">
                <Space>
                  <span className="context-emoji">{simulationInfo.emoji}</span>
                  <Text strong className="context-title">
                    {t('SIMULATION_CONTEXT', 'Bối cảnh mô phỏng')}
                  </Text>
                </Space>
              </div>
              <Text type="secondary" className="context-description">
                {simulationInfo.description}
              </Text>
            </div>

            {/* AI Scenarios Tabs */}
            <div className="scenarios-tabs-container">
              <AIScenarioTabs
                ref={scenarioTabsRef}
                courseId={courseId}
                onScenariosChange={onScenariosChange}
                selectedSimulationType={selectedSimulationType}
                onTaskAdd={onTaskAdd}
                onTaskUpdate={onTaskUpdate}
                onTaskDelete={onTaskDelete}
              />
            </div>

            {/* Help section */}
            <div className="scenarios-help">
              <Alert
                message={
                  <Space>
                    <CheckCircleOutlined />
                    {t('AI_SCENARIOS_TIPS', 'Mẹo sử dụng kịch bản AI')}
                  </Space>
                }
                description={
                  <div className="help-content">
                    <ul>
                      <li>{t('TIP_1', 'Tạo nhiều kịch bản khác nhau để đa dạng hóa trải nghiệm học tập')}</li>
                      <li>{t('TIP_2', 'Sử dụng AI Persona phù hợp với từng kịch bản cụ thể')}</li>
                      <li>{t('TIP_3', 'Thiết lập các nhiệm vụ đánh giá rõ ràng cho mỗi kịch bản')}</li>
                      <li>{t('TIP_4', 'Kiểm tra và điều chỉnh kịch bản thường xuyên để tối ưu hiệu quả')}</li>
                    </ul>
                  </div>
                }
                type="success"
                showIcon={false}
                className="help-alert"
              />
            </div>
          </>
        )}
      </div>
    </Card>
  );
};

export default AIScenariosCard;
