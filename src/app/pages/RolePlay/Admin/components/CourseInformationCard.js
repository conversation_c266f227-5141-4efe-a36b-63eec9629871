import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Row, Col, Form, Input, Select, Typography, Tooltip, Space, Divider } from 'antd';
import {
  InfoCircleOutlined,
  PlusOutlined,
  FileTextOutlined,
  SettingOutlined,
  StarOutlined,
} from '@ant-design/icons';

import { BUTTON } from '@constant';
import AntButton from '@component/AntButton';
import UploadImagePreview from '@component/UploadImagePreview';

import './CourseInformationCard.scss';

const { TextArea } = Input;
const { Option } = Select;
const { Text, Title } = Typography;

const SIMULATION_TYPES = ['Sale', 'Service', 'HR', 'Education', 'Other'];

const CourseInformationCard = ({
  form,
  thumbnailId,
  isLoadingThumbnail,
  selectedSimulationType,
  onUploadThumbnail,
  onClearThumbnail,
  onSimulationTypeChange,
  onShowGoogleSheetsImport,
}) => {
  const { t } = useTranslation();

  const getSimulationTypeIcon = (type) => {
    switch (type) {
      case 'Sale':
        return '💼';
      case 'Service':
        return '🛎️';
      case 'HR':
        return '👥';
      case 'Education':
        return '📚';
      default:
        return '⚙️';
    }
  };

  const getSimulationTypeColor = (type) => {
    switch (type) {
      case 'Sale':
        return '#52c41a';
      case 'Service':
        return '#1890ff';
      case 'HR':
        return '#722ed1';
      case 'Education':
        return '#fa8c16';
      default:
        return '#8c8c8c';
    }
  };

  return (
    <Card
      className="course-information-card"
      title={
        <div className="card-title-section">
          <div className="card-title-main">
            <FileTextOutlined className="card-title-icon" />
            <Title level={4} className="card-title">
              {t('COURSE_INFORMATION', 'Thông tin khóa học')}
            </Title>
          </div>
          <Text type="secondary" className="card-subtitle">
            {t('COURSE_BASIC_INFO_DESC', 'Nhập thông tin cơ bản về khóa học của bạn')}
          </Text>
        </div>
      }
      extra={
        <AntButton
          type={BUTTON.DEEP_NAVY}
          size="small"
          icon={<PlusOutlined />}
          onClick={onShowGoogleSheetsImport}
          className="import-button"
        >
          {t('IMPORT_FROM_LMS', 'Lấy dữ liệu LMS')}
        </AntButton>
      }
    >
      <div className="course-form-content">
        {/* Basic Information Section */}
        <div className="form-section">
          <div className="section-header">
            <SettingOutlined className="section-icon" />
            <Text strong className="section-title">
              {t('BASIC_INFORMATION', 'Thông tin cơ bản')}
            </Text>
          </div>

          <Row gutter={[24, 0]}>
            <Col xs={24} lg={14}>
              <Form.Item
                name="name"
                label={
                  <div className="form-label-with-icon">
                    <span className="required-indicator">*</span>
                    {t('COURSE_NAME', 'Tên khóa học')}
                    <Tooltip title={t('COURSE_NAME_TOOLTIP', 'Tên khóa học sẽ hiển thị cho học viên')}>
                      <InfoCircleOutlined className="info-icon" />
                    </Tooltip>
                  </div>
                }
                rules={[
                  { required: true, message: t('PLEASE_INPUT_COURSE_NAME', 'Vui lòng nhập tên khóa học!') },
                  { max: 255, message: t('COURSE_NAME_TOO_LONG', 'Tên khóa học không được vượt quá 255 ký tự') },
                ]}
                className="enhanced-form-item"
              >
                <Input
                  placeholder={t('ENTER_COURSE_NAME', 'Nhập tên khóa học...')}
                  className="enhanced-input"
                  size="large"
                />
              </Form.Item>
            </Col>

            <Col xs={24} lg={10}>
              <Form.Item
                name="status"
                label={
                  <div className="form-label-with-icon">
                    <span className="required-indicator">*</span>
                    {t('COURSE_STATUS', 'Trạng thái khóa học')}
                    <Tooltip title={t('COURSE_STATUS_TOOLTIP', 'Chọn trạng thái hiển thị của khóa học')}>
                      <InfoCircleOutlined className="info-icon" />
                    </Tooltip>
                  </div>
                }
                rules={[{ required: true, message: t('PLEASE_SELECT_COURSE_STATUS', 'Vui lòng chọn trạng thái khóa học!') }]}
                className="enhanced-form-item"
              >
                <Select
                  placeholder={t('SELECT_COURSE_STATUS', 'Chọn trạng thái...')}
                  className="enhanced-select"
                  size="large"
                  allowClear
                >
                  <Option value="draft">
                    <Space>
                      <div className="status-indicator draft" />
                      {t('DRAFT', 'Bản nháp')}
                    </Space>
                  </Option>
                  <Option value="published">
                    <Space>
                      <div className="status-indicator published" />
                      {t('PUBLISHED', 'Đã xuất bản')}
                    </Space>
                  </Option>
                  <Option value="archived">
                    <Space>
                      <div className="status-indicator archived" />
                      {t('ARCHIVED', 'Đã lưu trữ')}
                    </Space>
                  </Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </div>

        <Divider className="section-divider" />

        {/* Thumbnail and Description Section */}
        <div className="form-section">
          <div className="section-header">
            <StarOutlined className="section-icon" />
            <Text strong className="section-title">
              {t('VISUAL_CONTENT', 'Nội dung trực quan')}
            </Text>
          </div>

          <Row gutter={[24, 24]}>
            <Col xs={24} md={8}>
              <Form.Item
                label={
                  <div className="form-label-with-icon">
                    {t('COURSE_THUMBNAIL', 'Ảnh đại diện khóa học')}
                    <Tooltip title={t('THUMBNAIL_TOOLTIP', 'Ảnh đại diện sẽ hiển thị trong danh sách khóa học')}>
                      <InfoCircleOutlined className="info-icon" />
                    </Tooltip>
                  </div>
                }
                className="thumbnail-form-item"
              >
                <div className="thumbnail-upload-container">
                  <UploadImagePreview
                    imageId={thumbnailId}
                    onDrop={onUploadThumbnail}
                    onClear={onClearThumbnail}
                    loading={isLoadingThumbnail}
                  />
                  <div className="thumbnail-info">
                    <Text type="secondary" className="thumbnail-hint">
                      {t('THUMBNAIL_HINT', 'Kích thước khuyến nghị: 400x300px')}
                    </Text>
                    <Text type="secondary" className="thumbnail-format">
                      {t('SUPPORTED_FORMATS', 'Hỗ trợ: JPG, PNG')}
                    </Text>
                  </div>
                </div>
              </Form.Item>
              <Form.Item name="thumbnailId" hidden>
                <Input />
              </Form.Item>
            </Col>

            <Col xs={24} md={16}>
              <Form.Item
                name="description"
                label={
                  <div className="form-label-with-icon">
                    {t('COURSE_DESCRIPTION', 'Mô tả khóa học')}
                    <Tooltip title={t('DESCRIPTION_TOOLTIP', 'Mô tả chi tiết về nội dung và mục tiêu của khóa học')}>
                      <InfoCircleOutlined className="info-icon" />
                    </Tooltip>
                  </div>
                }
                rules={[{ max: 5000, message: t('DESCRIPTION_TOO_LONG', 'Mô tả không được vượt quá 5000 ký tự') }]}
                className="enhanced-form-item"
              >
                <TextArea
                  rows={6}
                  placeholder={t('ENTER_COURSE_DESCRIPTION', 'Nhập mô tả chi tiết về khóa học, mục tiêu học tập, đối tượng học viên...')}
                  className="enhanced-textarea"
                  showCount
                  maxLength={5000}
                />
              </Form.Item>
            </Col>
          </Row>
        </div>

        <Divider className="section-divider" />

        {/* Simulation Type Section */}
        <div className="form-section">
          <div className="section-header">
            <div className="section-icon simulation-icon">🎯</div>
            <Text strong className="section-title">
              {t('SIMULATION_SETTINGS', 'Cài đặt mô phỏng')}
            </Text>
          </div>

          <Row gutter={[24, 0]}>
            <Col xs={24} md={12}>
              <Form.Item
                name="simulationType"
                label={
                  <div className="form-label-with-icon">
                    <span className="required-indicator">*</span>
                    {t('SIMULATION_TYPE', 'Loại mô phỏng')}
                    <Tooltip title={t('SIMULATION_TYPE_TOOLTIP', 'Chọn loại mô phỏng phù hợp với mục tiêu đào tạo')}>
                      <InfoCircleOutlined className="info-icon" />
                    </Tooltip>
                  </div>
                }
                rules={[
                  { required: true, message: t('PLEASE_SELECT_SIMULATION_TYPE', 'Vui lòng chọn loại mô phỏng!') },
                ]}
                className="enhanced-form-item"
              >
                <Select
                  placeholder={t('SELECT_SIMULATION_TYPE', 'Chọn loại mô phỏng...')}
                  className="enhanced-select simulation-select"
                  size="large"
                  allowClear
                  onChange={onSimulationTypeChange}
                >
                  {SIMULATION_TYPES.map(type => (
                    <Option key={type} value={type}>
                      <Space>
                        <span className="simulation-emoji">{getSimulationTypeIcon(type)}</span>
                        <span>{t(type.toUpperCase() + '_SIMULATION', type)}</span>
                        <div
                          className="simulation-color-indicator"
                          style={{ backgroundColor: getSimulationTypeColor(type) }}
                        />
                      </Space>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>

            {selectedSimulationType && (
              <Col xs={24} md={12}>
                <div className="simulation-preview">
                  <div className="simulation-preview-header">
                    <span className="simulation-emoji-large">
                      {getSimulationTypeIcon(selectedSimulationType)}
                    </span>
                    <div>
                      <Text strong className="simulation-preview-title">
                        {t(selectedSimulationType.toUpperCase() + '_SIMULATION', selectedSimulationType)}
                      </Text>
                      <br />
                      <Text type="secondary" className="simulation-preview-desc">
                        {t(selectedSimulationType.toUpperCase() + '_SIMULATION_DESC', 
                          `Mô phỏng tình huống ${selectedSimulationType.toLowerCase()}`)}
                      </Text>
                    </div>
                  </div>
                </div>
              </Col>
            )}
          </Row>
        </div>
      </div>
    </Card>
  );
};

export default CourseInformationCard;
