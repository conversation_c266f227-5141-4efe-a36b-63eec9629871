.reference-materials-card {
  .ant-card-head {
    background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);
    border-bottom: 1px solid #e8f5e8;
    padding: 0 24px;

    .ant-card-head-wrapper {
      align-items: flex-start;
    }

    .ant-card-head-title {
      padding: 20px 0 16px 0;
    }

    .ant-card-extra {
      padding: 16px 0;
    }
  }

  .card-title-section {
    .card-title-main {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 4px;

      .card-title-icon {
        color: #52c41a;
        font-size: 18px;
      }

      .card-title {
        margin: 0;
        color: #262626;
        font-size: 18px;
        font-weight: 600;
      }

      .reference-count-badge {
        .ant-badge-count {
          background-color: #52c41a;
          box-shadow: 0 0 0 1px #fff;
        }
      }
    }

    .card-subtitle {
      font-size: 13px;
      line-height: 1.4;
    }
  }

  .add-reference-button {
    border-radius: 8px;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
    }
  }

  // Empty state
  .reference-empty-state {
    padding: 60px 20px;
    text-align: center;

    .empty-state-content {
      max-width: 400px;
      margin: 0 auto;

      .empty-state-icon {
        font-size: 64px;
        color: #d9d9d9;
        margin-bottom: 16px;
      }

      .empty-state-title {
        color: #262626;
        margin-bottom: 8px;
      }

      .empty-state-description {
        margin-bottom: 24px;
        line-height: 1.6;
      }

      .empty-state-button {
        border-radius: 8px;
        height: 48px;
        padding: 0 24px;
        font-weight: 500;
      }
    }
  }

  // Content with references
  .reference-materials-content {
    .reference-controls {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding: 16px;
      background: #fafafa;
      border-radius: 8px;
      border: 1px solid #f0f0f0;

      .reference-controls-left {
        display: flex;
        gap: 12px;
        flex: 1;

        .reference-search {
          width: 280px;

          .ant-input {
            border-radius: 6px;
          }
        }

        .reference-filter {
          width: 120px;

          .ant-select-selector {
            border-radius: 6px;
          }
        }
      }

      .reference-controls-right {
        display: flex;
        gap: 12px;
        align-items: center;

        .sort-button {
          border-radius: 6px;
        }

        .view-mode-buttons {
          .ant-btn {
            border-radius: 6px;

            &:first-child {
              border-top-right-radius: 0;
              border-bottom-right-radius: 0;
            }

            &:last-child {
              border-top-left-radius: 0;
              border-bottom-left-radius: 0;
            }
          }
        }
      }
    }

    .reference-results-info {
      margin-bottom: 16px;
      padding-left: 4px;
    }

    // Card view
    .reference-content.card-view {
      .reference-card-wrapper {
        height: 100%;

        .reference-card {
          height: 100%;
          border-radius: 12px;
          border: 1px solid #f0f0f0;
          transition: all 0.2s ease;

          &:hover {
            border-color: #1890ff;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
            transform: translateY(-2px);
          }

          .ant-card-body {
            padding: 16px;
            height: 100%;
          }

          .reference-card-content {
            display: flex;
            flex-direction: column;
            height: 100%;

            .reference-header {
              display: flex;
              gap: 12px;
              margin-bottom: 12px;

              .reference-icon-wrapper {
                flex-shrink: 0;

                .reference-icon {
                  width: 40px;
                  height: 40px;
                  border-radius: 8px;
                  background: #f5f5f5;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  font-size: 18px;
                }
              }

              .reference-title-section {
                flex: 1;
                min-width: 0;

                .reference-title {
                  margin-bottom: 8px;

                  .reference-link {
                    color: #262626;
                    font-weight: 500;
                    font-size: 14px;
                    line-height: 1.4;
                    display: -webkit-box;
                    -webkit-line-clamp: 2;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                    text-decoration: none;

                    &:hover {
                      color: #1890ff;
                    }
                  }
                }

                .reference-tags {
                  display: flex;
                  gap: 4px;
                  flex-wrap: wrap;

                  .reference-type-tag,
                  .reference-visibility-tag {
                    font-size: 11px;
                    border-radius: 4px;
                    margin: 0;
                  }
                }
              }
            }

            .reference-meta {
              margin-bottom: 12px;

              .reference-date {
                display: flex;
                align-items: center;
                gap: 6px;
                color: #8c8c8c;
                font-size: 12px;

                .anticon {
                  font-size: 12px;
                }
              }
            }

            .reference-actions {
              margin-top: auto;
              padding-top: 12px;
              border-top: 1px solid #f0f0f0;

              .action-button {
                border-radius: 6px;
                transition: all 0.2s ease;

                &.view:hover {
                  color: #1890ff;
                  background-color: #f0f8ff;
                }

                &.download:hover {
                  color: #52c41a;
                  background-color: #f6ffed;
                }

                &.edit:hover {
                  color: #fa8c16;
                  background-color: #fff7e6;
                }

                &.delete:hover {
                  color: #ff4d4f;
                  background-color: #fff2f0;
                }
              }
            }
          }
        }
      }
    }

    // List view
    .reference-content.list-view {
      .reference-list {
        .reference-list-item {
          display: flex;
          align-items: center;
          gap: 16px;
          padding: 16px;
          border: 1px solid #f0f0f0;
          border-radius: 8px;
          margin-bottom: 8px;
          transition: all 0.2s ease;

          &:hover {
            border-color: #1890ff;
            background-color: #f0f8ff;
          }

          .reference-list-icon {
            flex-shrink: 0;
            width: 32px;
            height: 32px;
            border-radius: 6px;
            background: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
          }

          .reference-list-content {
            flex: 1;
            min-width: 0;

            .reference-list-title {
              margin-bottom: 4px;

              .reference-list-link {
                color: #262626;
                font-weight: 500;
                font-size: 14px;
                text-decoration: none;
                display: block;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;

                &:hover {
                  color: #1890ff;
                }
              }
            }

            .reference-list-meta {
              .reference-list-date {
                font-size: 12px;
              }
            }
          }

          .reference-list-actions {
            flex-shrink: 0;
          }
        }
      }
    }

    // No results
    .no-results {
      padding: 40px 20px;
      text-align: center;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .reference-materials-card {
    .ant-card-head {
      padding: 0 16px;

      .card-title-section {
        .card-title-main {
          .card-title {
            font-size: 16px;
          }
        }

        .card-subtitle {
          font-size: 12px;
        }
      }
    }

    .reference-materials-content {
      .reference-controls {
        flex-direction: column;
        gap: 12px;
        align-items: stretch;

        .reference-controls-left {
          .reference-search {
            width: 100%;
          }
        }

        .reference-controls-right {
          justify-content: space-between;
        }
      }

      .reference-content.card-view {
        .reference-card {
          .reference-card-content {
            .reference-header {
              .reference-icon-wrapper .reference-icon {
                width: 36px;
                height: 36px;
                font-size: 16px;
              }

              .reference-title-section {
                .reference-title .reference-link {
                  font-size: 13px;
                }
              }
            }
          }
        }
      }

      .reference-content.list-view {
        .reference-list {
          .reference-list-item {
            padding: 12px;

            .reference-list-icon {
              width: 28px;
              height: 28px;
              font-size: 14px;
            }

            .reference-list-content {
              .reference-list-title .reference-list-link {
                font-size: 13px;
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .reference-materials-card {
    .ant-card-head {
      .ant-card-head-wrapper {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
      }

      .ant-card-extra {
        padding: 0 0 16px 0;
        width: 100%;

        .add-reference-button {
          width: 100%;
        }
      }
    }

    .reference-materials-content {
      .reference-controls {
        .reference-controls-right {
          flex-direction: column;
          gap: 8px;

          .view-mode-buttons {
            width: 100%;

            .ant-btn {
              flex: 1;
            }
          }
        }
      }
    }
  }
}
