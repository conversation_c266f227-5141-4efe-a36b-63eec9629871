.course-detail-header-card {
  margin-bottom: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;

  .ant-card-body {
    padding: 20px 24px;
  }

  .course-detail-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 24px;

    .course-detail-header-left {
      display: flex;
      align-items: flex-start;
      gap: 16px;
      flex: 1;
      min-width: 0; // Prevent flex item from overflowing

      .back-button {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 12px;
        border-radius: 8px;
        color: #595959;
        font-weight: 500;
        transition: all 0.2s ease;
        white-space: nowrap;

        &:hover {
          color: #1890ff;
          background-color: #f0f8ff;
        }

        .anticon {
          font-size: 14px;
        }
      }

      .header-divider {
        height: 32px;
        margin: 8px 0;
        border-color: #e8e8e8;
      }

      .course-detail-title-section {
        flex: 1;
        min-width: 0;

        .course-detail-title {
          margin: 0 0 8px 0;
          font-size: 24px;
          font-weight: 600;
          color: #262626;
          line-height: 1.3;
          
          // Truncate long titles
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .course-detail-meta {
          .course-status {
            display: flex;
            align-items: center;
            gap: 8px;

            .status-indicator {
              width: 8px;
              height: 8px;
              border-radius: 50%;
              flex-shrink: 0;
            }

            .status-text {
              font-weight: 500;
              font-size: 14px;
            }
          }

          .simulation-type {
            .ant-typography {
              font-size: 14px;
            }
          }
        }
      }
    }

    .course-detail-header-right {
      display: flex;
      align-items: flex-start;
      flex-shrink: 0;

      .header-divider {
        height: 32px;
        margin: 8px 0;
        border-color: #e8e8e8;
      }

      .action-button {
        border-radius: 8px;
        font-weight: 500;
        transition: all 0.2s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);

        &.secondary {
          border-color: #d9d9d9;
          color: #595959;

          &:hover {
            border-color: #1890ff;
            color: #1890ff;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
          }
        }

        &.cancel {
          border-color: #d9d9d9;
          color: #595959;

          &:hover {
            border-color: #ff4d4f;
            color: #ff4d4f;
          }
        }

        &.primary {
          &:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
          }
        }

        .anticon {
          font-size: 14px;
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 1200px) {
  .course-detail-header-card {
    .course-detail-header {
      .course-detail-header-left {
        .course-detail-title-section {
          .course-detail-title {
            font-size: 22px;
          }
        }
      }

      .course-detail-header-right {
        .action-button {
          padding: 8px 16px;
          
          &.primary {
            padding: 8px 20px;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .course-detail-header-card {
    margin-bottom: 16px;
    border-radius: 8px;

    .ant-card-body {
      padding: 16px;
    }

    .course-detail-header {
      flex-direction: column;
      gap: 16px;

      .course-detail-header-left {
        width: 100%;
        
        .back-button {
          padding: 6px 8px;
          font-size: 14px;
        }

        .course-detail-title-section {
          .course-detail-title {
            font-size: 20px;
            white-space: normal;
            overflow: visible;
            text-overflow: unset;
          }

          .course-detail-meta {
            .ant-space {
              flex-wrap: wrap;
            }
          }
        }
      }

      .course-detail-header-right {
        width: 100%;
        justify-content: flex-end;

        .ant-space {
          flex-wrap: wrap;
          justify-content: flex-end;
        }

        .action-button {
          font-size: 14px;
          
          &.secondary {
            display: none; // Hide secondary buttons on mobile
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .course-detail-header-card {
    .course-detail-header {
      .course-detail-header-left {
        flex-direction: column;
        gap: 12px;

        .header-divider {
          display: none;
        }

        .back-button {
          align-self: flex-start;
        }
      }

      .course-detail-header-right {
        .action-button {
          flex: 1;
          min-width: 80px;
        }
      }
    }
  }
}
