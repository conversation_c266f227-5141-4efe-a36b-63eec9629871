import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';
import { Card, Button, Space, Typography, Divider, Tooltip } from 'antd';
import {
  ArrowLeftOutlined,
  SaveOutlined,
  EyeOutlined,
  MoreOutlined,
  SettingOutlined,
  Bar<PERSON><PERSON>Outlined,
} from '@ant-design/icons';

import { BUTTON } from '@constant';
import { LINK } from '@link';
import AntButton from '@component/AntButton';

import './CourseDetailHeader.scss';

const { Title, Text } = Typography;

const CourseDetailHeader = ({
  isEditMode,
  isLoading,
  onSave,
  onCancel,
  courseData,
  saveButtonDisabled,
}) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { id } = useParams();

  const handleBackToCourseManagement = () => {
    navigate(LINK.ADMIN.ROLE_PLAY_COURSE_MANAGEMENT);
  };

  const handleViewStatistics = () => {
    if (id) {
      navigate(LINK.ADMIN.ROLE_PLAY_COURSE_STATISTICS.format(id));
    }
  };

  const handlePreviewCourse = () => {
    if (id) {
      // Navigate to student view of the course
      window.open(LINK.ROLE_PLAY_COURSE_DETAIL.format(id), '_blank');
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'published':
        return '#52c41a';
      case 'draft':
        return '#faad14';
      case 'archived':
        return '#8c8c8c';
      default:
        return '#d9d9d9';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'published':
        return t('PUBLISHED', 'Đã xuất bản');
      case 'draft':
        return t('DRAFT', 'Bản nháp');
      case 'archived':
        return t('ARCHIVED', 'Đã lưu trữ');
      default:
        return t('UNKNOWN', 'Không xác định');
    }
  };

  return (
    <Card className="course-detail-header-card">
      <div className="course-detail-header">
        <div className="course-detail-header-left">
          <Button
            type="text"
            icon={<ArrowLeftOutlined />}
            onClick={handleBackToCourseManagement}
            className="back-button"
          >
            {t('BACK_TO_COURSE_MANAGEMENT', 'Quay lại quản lý khóa học')}
          </Button>
          
          <Divider type="vertical" className="header-divider" />
          
          <div className="course-detail-title-section">
            <Title level={2} className="course-detail-title">
              {isEditMode ? 
                (courseData?.name || t('EDIT_COURSE', 'Chỉnh sửa khóa học')) : 
                t('CREATE_COURSE', 'Tạo khóa học mới')
              }
            </Title>
            
            {isEditMode && courseData && (
              <div className="course-detail-meta">
                <Space size="middle">
                  <div className="course-status">
                    <div 
                      className="status-indicator"
                      style={{ backgroundColor: getStatusColor(courseData.status) }}
                    />
                    <Text className="status-text">
                      {getStatusText(courseData.status)}
                    </Text>
                  </div>
                  
                  {courseData.simulationType && (
                    <div className="simulation-type">
                      <Text type="secondary">
                        {t('SIMULATION_TYPE', 'Loại mô phỏng')}: {courseData.simulationType}
                      </Text>
                    </div>
                  )}
                </Space>
              </div>
            )}
          </div>
        </div>

        <div className="course-detail-header-right">
          <Space size="middle">
            {isEditMode && id && (
              <>
                <Tooltip title={t('VIEW_STATISTICS', 'Xem thống kê')}>
                  <Button
                    icon={<BarChartOutlined />}
                    onClick={handleViewStatistics}
                    className="action-button secondary"
                  >
                    {t('STATISTICS', 'Thống kê')}
                  </Button>
                </Tooltip>

                <Tooltip title={t('PREVIEW_COURSE', 'Xem trước khóa học')}>
                  <Button
                    icon={<EyeOutlined />}
                    onClick={handlePreviewCourse}
                    className="action-button secondary"
                  >
                    {t('PREVIEW', 'Xem trước')}
                  </Button>
                </Tooltip>

                <Divider type="vertical" className="header-divider" />
              </>
            )}

            <Button
              size="large"
              onClick={onCancel}
              className="action-button cancel"
              disabled={saveButtonDisabled}
            >
              {t('CANCEL', 'Hủy')}
            </Button>

            <AntButton
              type={BUTTON.DEEP_NAVY}
              size="large"
              icon={<SaveOutlined />}
              onClick={onSave}
              className="action-button primary"
              loading={isLoading}
              disabled={saveButtonDisabled}
            >
              {isEditMode ? t('SAVE_CHANGES', 'Lưu thay đổi') : t('CREATE_COURSE', 'Tạo khóa học')}
            </AntButton>
          </Space>
        </div>
      </div>
    </Card>
  );
};

export default CourseDetailHeader;
