.course-information-card {
  .ant-card-head {
    background: linear-gradient(135deg, #f8faff 0%, #f0f8ff 100%);
    border-bottom: 1px solid #e8f4fd;
    padding: 0 24px;

    .ant-card-head-wrapper {
      align-items: flex-start;
    }

    .ant-card-head-title {
      padding: 20px 0 16px 0;
    }

    .ant-card-extra {
      padding: 16px 0;
    }
  }

  .card-title-section {
    .card-title-main {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 4px;

      .card-title-icon {
        color: #1890ff;
        font-size: 18px;
      }

      .card-title {
        margin: 0;
        color: #262626;
        font-size: 18px;
        font-weight: 600;
      }
    }

    .card-subtitle {
      font-size: 13px;
      line-height: 1.4;
    }
  }

  .import-button {
    border-radius: 8px;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
    transition: all 0.2s ease;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
    }
  }

  .course-form-content {
    .form-section {
      margin-bottom: 32px;

      &:last-child {
        margin-bottom: 0;
      }

      .section-header {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-bottom: 20px;
        padding-bottom: 8px;
        border-bottom: 2px solid #f0f0f0;

        .section-icon {
          color: #1890ff;
          font-size: 16px;

          &.simulation-icon {
            font-size: 18px;
          }
        }

        .section-title {
          font-size: 15px;
          color: #262626;
          font-weight: 600;
        }
      }
    }

    .section-divider {
      margin: 32px 0;
      border-color: #f0f0f0;
    }

    // Enhanced form items
    .enhanced-form-item {
      .ant-form-item-label {
        padding-bottom: 8px;

        .form-label-with-icon {
          display: flex;
          align-items: center;
          gap: 6px;

          .required-indicator {
            color: #ff4d4f;
            font-weight: 600;
          }

          .info-icon {
            color: #8c8c8c;
            font-size: 12px;
            cursor: help;
            transition: color 0.2s ease;

            &:hover {
              color: #1890ff;
            }
          }
        }

        > label {
          font-weight: 500;
          color: #262626;
          font-size: 14px;
        }
      }

      .ant-form-item-control-input {
        min-height: 48px;
        display: flex;
        align-items: center;
      }
    }

    // Enhanced inputs
    .enhanced-input,
    .enhanced-textarea {
      border-radius: 8px;
      border: 1.5px solid #d9d9d9;
      transition: all 0.2s ease;
      font-size: 14px;

      &:hover {
        border-color: #40a9ff;
      }

      &:focus,
      &.ant-input-focused {
        border-color: #1890ff;
        box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1);
      }

      &::placeholder {
        color: #bfbfbf;
        font-style: italic;
      }
    }

    .enhanced-input {
      padding: 12px 16px;
      height: 48px;
    }

    .enhanced-textarea {
      padding: 12px 16px;
      line-height: 1.6;
      resize: vertical;

      &.ant-input {
        min-height: 120px;
      }
    }

    // Enhanced select
    .enhanced-select {
      .ant-select-selector {
        border-radius: 8px !important;
        border: 1.5px solid #d9d9d9 !important;
        padding: 8px 16px !important;
        min-height: 48px !important;
        display: flex !important;
        align-items: center !important;
        transition: all 0.2s ease !important;

        .ant-select-selection-placeholder {
          color: #bfbfbf;
          font-style: italic;
        }
      }

      &:hover .ant-select-selector {
        border-color: #40a9ff !important;
      }

      &.ant-select-focused .ant-select-selector {
        border-color: #1890ff !important;
        box-shadow: 0 0 0 3px rgba(24, 144, 255, 0.1) !important;
      }
    }

    // Status indicators in select options
    .status-indicator {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      display: inline-block;

      &.draft {
        background-color: #faad14;
      }

      &.published {
        background-color: #52c41a;
      }

      &.archived {
        background-color: #8c8c8c;
      }
    }

    // Thumbnail section
    .thumbnail-form-item {
      .thumbnail-upload-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 12px;

        .upload-image-preview {
          width: 140px !important;
          height: 140px !important;
          border-radius: 12px;
          border: 2px dashed #d9d9d9;
          transition: all 0.2s ease;

          &:hover {
            border-color: #1890ff;
            background-color: #f0f8ff;
          }

          .upload-image-preview__inner {
            border-radius: 10px;
            overflow: hidden;

            img {
              border-radius: 10px;
            }
          }
        }

        .thumbnail-info {
          text-align: center;

          .thumbnail-hint,
          .thumbnail-format {
            display: block;
            font-size: 12px;
            line-height: 1.4;
          }

          .thumbnail-hint {
            color: #595959;
            margin-bottom: 2px;
          }

          .thumbnail-format {
            color: #8c8c8c;
          }
        }
      }
    }

    // Simulation type section
    .simulation-select {
      .ant-select-dropdown {
        .ant-select-item {
          padding: 12px 16px;

          .simulation-emoji {
            font-size: 16px;
            margin-right: 8px;
          }

          .simulation-color-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: auto;
          }
        }
      }
    }

    .simulation-preview {
      background: linear-gradient(135deg, #f8faff 0%, #f0f8ff 100%);
      border: 1px solid #e8f4fd;
      border-radius: 12px;
      padding: 20px;
      margin-top: 8px;

      .simulation-preview-header {
        display: flex;
        align-items: flex-start;
        gap: 16px;

        .simulation-emoji-large {
          font-size: 32px;
          line-height: 1;
        }

        .simulation-preview-title {
          font-size: 16px;
          color: #262626;
        }

        .simulation-preview-desc {
          font-size: 13px;
          line-height: 1.4;
        }
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .course-information-card {
    .ant-card-head {
      padding: 0 16px;

      .card-title-section {
        .card-title-main {
          .card-title {
            font-size: 16px;
          }
        }

        .card-subtitle {
          font-size: 12px;
        }
      }
    }

    .course-form-content {
      .form-section {
        margin-bottom: 24px;

        .section-header {
          margin-bottom: 16px;

          .section-title {
            font-size: 14px;
          }
        }
      }

      .section-divider {
        margin: 24px 0;
      }

      .enhanced-form-item {
        .ant-form-item-control-input {
          min-height: 44px;
        }
      }

      .enhanced-input {
        height: 44px;
        padding: 10px 14px;
      }

      .enhanced-select .ant-select-selector {
        min-height: 44px !important;
        padding: 6px 14px !important;
      }

      .thumbnail-form-item {
        .thumbnail-upload-container {
          .upload-image-preview {
            width: 120px !important;
            height: 120px !important;
          }
        }
      }

      .simulation-preview {
        padding: 16px;

        .simulation-preview-header {
          gap: 12px;

          .simulation-emoji-large {
            font-size: 28px;
          }

          .simulation-preview-title {
            font-size: 15px;
          }
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .course-information-card {
    .ant-card-head {
      .ant-card-head-wrapper {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
      }

      .ant-card-extra {
        padding: 0 0 16px 0;
        width: 100%;

        .import-button {
          width: 100%;
        }
      }
    }

    .course-form-content {
      .thumbnail-form-item {
        .thumbnail-upload-container {
          .upload-image-preview {
            width: 100px !important;
            height: 100px !important;
          }
        }
      }
    }
  }
}
