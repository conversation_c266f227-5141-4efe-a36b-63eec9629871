import React, { useState } from 'react';
import ReferenceMaterialsCard from './ReferenceMaterialsCard';

// Demo component để test ReferenceMaterialsCard
const ReferenceMaterialsCardDemo = () => {
  const [referenceViewMode, setReferenceViewMode] = useState('card');

  const mockReferences = [
    {
      _id: '1',
      name: '<PERSON><PERSON><PERSON> liệu hướng dẫn bán hàng.pdf',
      type: 'file',
      fileId: 'file-id-1',
      isPublic: true,
      createdAt: '2024-01-15T10:30:00Z',
      content: 'Nội dung tài liệu...'
    },
    {
      _id: '2',
      name: 'Website chính thức của công ty',
      type: 'url',
      url: 'https://example.com',
      isPublic: false,
      createdAt: '2024-01-14T09:15:00Z'
    },
    {
      _id: '3',
      name: '<PERSON><PERSON>ng gi<PERSON> sản phẩm 2024.xlsx',
      type: 'file',
      fileId: 'file-id-3',
      isPublic: true,
      createdAt: '2024-01-13T14:45:00Z'
    },
    {
      _id: '4',
      name: 'Slide thuyết trình khóa học.pptx',
      type: 'file',
      fileId: 'file-id-4',
      isPublic: false,
      createdAt: '2024-01-12T16:20:00Z',
      content: 'Nội dung slide...'
    }
  ];

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('vi-VN');
  };

  const getFileIcon = (name, type) => {
    if (type === 'url') return '🔗';
    
    const extension = name?.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return '📄';
      case 'doc':
      case 'docx':
        return '📝';
      case 'xls':
      case 'xlsx':
        return '📊';
      case 'ppt':
      case 'pptx':
        return '📋';
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return '🖼️';
      default:
        return '📁';
    }
  };

  const handleAddReference = () => {
    console.log('Add reference clicked');
  };

  const handleEditReference = (reference) => {
    console.log('Edit reference:', reference);
  };

  const handleDeleteReference = (referenceId) => {
    console.log('Delete reference:', referenceId);
  };

  const handleViewContent = (reference) => {
    console.log('View content:', reference);
  };

  return (
    <div style={{ padding: '24px', background: '#f5f5f5', minHeight: '100vh' }}>
      <h1>Reference Materials Card Demo</h1>
      
      <h2>With References</h2>
      <ReferenceMaterialsCard
        references={mockReferences}
        referenceViewMode={referenceViewMode}
        onViewModeChange={setReferenceViewMode}
        onAddReference={handleAddReference}
        onEditReference={handleEditReference}
        onDeleteReference={handleDeleteReference}
        onViewContent={handleViewContent}
        formatDate={formatDate}
        getFileIcon={getFileIcon}
      />

      <h2>Empty State</h2>
      <ReferenceMaterialsCard
        references={[]}
        referenceViewMode={referenceViewMode}
        onViewModeChange={setReferenceViewMode}
        onAddReference={handleAddReference}
        onEditReference={handleEditReference}
        onDeleteReference={handleDeleteReference}
        onViewContent={handleViewContent}
        formatDate={formatDate}
        getFileIcon={getFileIcon}
      />
    </div>
  );
};

export default ReferenceMaterialsCardDemo;
