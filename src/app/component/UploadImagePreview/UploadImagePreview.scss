.upload-image-preview {
  width: 140px;
  height: 140px;
  padding: 6px;
  border: 2px dashed #d9d9d9;
  border-radius: 12px;
  flex-shrink: 0;
  transition: all 0.2s ease;
  background-color: #fafafa;

  &:hover {
    border-color: #1890ff;
    background-color: #f0f8ff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
  }

  .upload-image-preview__inner {
    width: 100%;
    height: 100%;
    background: #ffffff;
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.06);

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 8px;
    }

    &:not(:hover) {
      .upload-image-preview__backdrop {
        display: none;
      }
    }

    .upload-image-preview__backdrop {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.6);
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      border-radius: 8px;
      backdrop-filter: blur(2px);
      transition: all 0.2s ease;
    }

    .upload-image-preview__upload {
      cursor: pointer;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      gap: 8px;
      font-size: 13px;
      font-weight: 500;
      user-select: none;
      color: #595959;
      transition: all 0.2s ease;

      &:hover {
        color: #1890ff;
      }

      svg {
        width: 24px;
        height: 24px;

        path {
          stroke: currentColor;
          stroke-width: 1.5;
        }
      }
    }
  }
}
